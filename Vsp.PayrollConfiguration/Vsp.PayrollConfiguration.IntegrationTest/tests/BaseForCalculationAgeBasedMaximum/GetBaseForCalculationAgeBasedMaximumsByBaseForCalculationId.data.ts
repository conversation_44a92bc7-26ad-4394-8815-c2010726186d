export const expectedResponses = {
    QA_BFC_AgeBasedMaximum_GET_CLA: {
        year2025: {
            baseForCalculation_1: {
                _embedded: [
                    {
                        "age": 15,
                        "baseForCalculation": {
                            "key": 1
                        },
                        "definedAtLevel": {
                            "id": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            },
                            "maximum": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "id": "000009f1-07e9-0001-0f00-010000000000",
                        "inheritanceLevel": {
                            "id": "a3fe2594-d694-4086-a56c-e521210d0d31",
                            "type": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "maximum": 10,
                        "startPayrollPeriod": {
                            "payrollPeriodId": 202501,
                            "periodEndDate": "2025-01-31",
                            "periodNumber": 1,
                            "periodStartDate": "2025-01-01",
                            "year": 2025
                        },
                        "year": 2025
                    },
                    {
                        "age": 18,
                        "baseForCalculation": {
                            "key": 1
                        },
                        "definedAtLevel": {
                            "id": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            },
                            "maximum": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "id": "000009f1-07e9-0001-1200-020000000000",
                        "inheritanceLevel": {
                            "id": "a3fe2594-d694-4086-a56c-e521210d0d31",
                            "type": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "maximum": 20,
                        "startPayrollPeriod": {
                            "payrollPeriodId": 202502,
                            "periodEndDate": "2025-02-28",
                            "periodNumber": 2,
                            "periodStartDate": "2025-02-01",
                            "year": 2025
                        },
                        "year": 2025
                    },
                    {
                        "age": 35,
                        "baseForCalculation": {
                            "key": 1
                        },
                        "definedAtLevel": {
                            "id": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            },
                            "maximum": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "id": "000009f1-07e9-0001-2300-030000000000",
                        "inheritanceLevel": {
                            "id": "a3fe2594-d694-4086-a56c-e521210d0d31",
                            "type": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "maximum": 30,
                        "startPayrollPeriod": {
                            "payrollPeriodId": 202503,
                            "periodEndDate": "2025-03-31",
                            "periodNumber": 3,
                            "periodStartDate": "2025-03-01",
                            "year": 2025
                        },
                        "year": 2025
                    },
                    {
                        "age": 50,
                        "baseForCalculation": {
                            "key": 1
                        },
                        "definedAtLevel": {
                            "id": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            },
                            "maximum": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "id": "000009f1-07e9-0001-3200-0a0000000000",
                        "inheritanceLevel": {
                            "id": "a3fe2594-d694-4086-a56c-e521210d0d31",
                            "type": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "maximum": 45,
                        "startPayrollPeriod": {
                            "payrollPeriodId": 202510,
                            "periodEndDate": "2025-10-31",
                            "periodNumber": 10,
                            "periodStartDate": "2025-10-01",
                            "year": 2025
                        },
                        "year": 2025
                    },
                    {
                        "age": 70,
                        "baseForCalculation": {
                            "key": 1
                        },
                        "definedAtLevel": {
                            "id": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            },
                            "maximum": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "id": "000009f1-07e9-0001-4600-0c0000000000",
                        "inheritanceLevel": {
                            "id": "a3fe2594-d694-4086-a56c-e521210d0d31",
                            "type": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "maximum": 25,
                        "startPayrollPeriod": {
                            "payrollPeriodId": 202512,
                            "periodEndDate": "2025-12-31",
                            "periodNumber": 12,
                            "periodStartDate": "2025-12-01",
                            "year": 2025
                        },
                        "year": 2025
                    }
                ]
            },
        }
    }
};


import {ITestConfig, testEndpoint, ValidationMode} from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./BaseForCalculationAgeBasedMinimum.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "basesforcalculation/agebasedminimums/{baseForCalculationAgeBasedMinimumId}",
    method: "PATCH",
    params: {
        baseForCalculationAgeBasedMinimumId: entityData.baseForCalculationAgeBasedMinimumIds.QA_BFC_AgeBasedMinimum_PATCH_CLA.year2025.ageBasedMinimum_50,
    },
    modules: {
        methodNotAllowed: ["GET", "PUT", "POST"],
        authorization: true,
        rechtBasis: false,
        requireBody: false, // Covered in C# integration tests
        token: "QA_PayrollConfiguration1",
        body: { "minimum": 123 },
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1
    },
    tests: {
        // ---200---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---400---
        // just one check to trigger a validation
        "PatchBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumId should return 400 and the correct message for invalid Minimum value": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    baseForCalculationAgeBasedMinimumId: entityData.baseForCalculationAgeBasedMinimumIds.QA_BFC_AgeBasedMinimum_PATCH_CLA.year2025.ageBasedMinimum_50,
                },
                timeout: 120000,
                body: { "minimum": 1000000},
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages[0].messageCode",
                        value: "ModelStateValidationError",
                        mode: ValidationMode.strict
                    },
                ]
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "PatchBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumId should return 403 when baseForCalculationAgeBasedMinimumId=non-EntityGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    baseForCalculationAgeBasedMinimumId: sharedData.nonEntityGuid,
                },
                body: { "minimum": 123 },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial
                    }
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
